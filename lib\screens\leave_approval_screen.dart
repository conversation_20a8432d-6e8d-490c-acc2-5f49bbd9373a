import 'package:flutter/material.dart';
import '../services/odoo_service.dart';
import '../models/pending_leave_request.dart';
import '../config/app_config.dart';

/// شاشة الموافقة على إجازات الموظفين للمديرين
class LeaveApprovalScreen extends StatefulWidget {
  final OdooService odooService;
  final int uid;
  final String password;

  const LeaveApprovalScreen({
    super.key,
    required this.odooService,
    required this.uid,
    required this.password,
  });

  @override
  State<LeaveApprovalScreen> createState() => _LeaveApprovalScreenState();
}

class _LeaveApprovalScreenState extends State<LeaveApprovalScreen> {
  List<PendingLeaveRequest> _allRequests = [];
  List<PendingLeaveRequest> _filteredRequests = [];
  bool _isLoading = true;
  String? _errorMessage;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedLeaveType = 'الكل';
  int _pendingCount = 0;
  int _approvedCount = 0;
  int _rejectedCount = 0;
  int _completCount = 0;
  final Set<int> _expandedCards = <int>{};
  String _currentFilter = 'all';
  bool _isFilterSectionExpanded = true;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _loadPendingRequests();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل طلبات الإجازة المعلقة
  Future<void> _loadPendingRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final requestsData = await widget.odooService.getPendingLeaveRequests(
        uid: widget.uid,
        password: widget.password,
      );

      setState(() {
        _allRequests = requestsData
            .map((data) => PendingLeaveRequest.fromOdooData(data))
            .toList();
        _calculateStatistics();
        _applyFilters();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ في تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  /// إعادة تحميل البيانات
  Future<void> _refreshData() async {
    await _loadPendingRequests();
  }

  void _calculateStatistics() {
    _pendingCount = _allRequests.where((r) => r.state == 'confirm').length;
    _approvedCount = _allRequests.where((r) => r.state == 'validate1').length;
    _rejectedCount = _allRequests.where((r) => r.state == 'refuse').length;
    _completCount = _allRequests.where((r) => r.state == 'validate').length;
  }

  void _applyFilters() {
    List<PendingLeaveRequest> filtered = List.from(_allRequests);

    if (_currentFilter != 'all') {
      if (_currentFilter == 'confirm') {
        filtered = filtered.where((r) => r.state == 'confirm').toList();
      } else if (_currentFilter == 'validate1') {
        filtered = filtered.where((r) => r.state == 'validate1').toList();
      } else if (_currentFilter == 'validate') {
        filtered = filtered.where((r) => r.state == 'validate').toList();
      } else if (_currentFilter == 'refuse') {
        filtered = filtered.where((r) => r.state == 'refuse').toList();
      }
    }

    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where(
            (r) =>
                r.employeeName.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                r.leaveTypeName.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ),
          )
          .toList();
    }

    if (_selectedLeaveType != 'الكل') {
      filtered = filtered
          .where((r) => r.leaveTypeName == _selectedLeaveType)
          .toList();
    }

    _filteredRequests = filtered;
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _applyFilters();
    });
  }

  void _applyFilter(String filterValue) {
    setState(() {
      _currentFilter = filterValue;
      _applyFilters();
    });
  }

  /// الموافقة على طلب إجازة
  Future<void> _approveRequest(PendingLeaveRequest request) async {
    final confirmed = await _showConfirmationDialog(
      title: 'الموافقة على الإجازة',
      message: 'هل تريد الموافقة على إجازة ${request.employeeName}؟',
      confirmText: 'موافق',
      confirmColor: Colors.green,
    );

    if (confirmed == true) {
      final success = await widget.odooService.approveLeaveRequest(
        uid: widget.uid,
        password: widget.password,
        leaveId: request.id,
      );

      if (success) {
        _showSnackBar('تم الموافقة على الإجازة بنجاح', Colors.green);
        _refreshData();
      } else {
        _showSnackBar('فشل في الموافقة على الإجازة', Colors.red);
      }
    }
  }

  /// رفض طلب إجازة
  Future<void> _rejectRequest(PendingLeaveRequest request) async {
    final confirmed = await _showConfirmationDialog(
      title: 'رفض الإجازة',
      message: 'هل تريد رفض إجازة ${request.employeeName}؟',
      confirmText: 'رفض',
      confirmColor: Colors.red,
    );

    if (confirmed == true) {
      final success = await widget.odooService.rejectLeaveRequest(
        uid: widget.uid,
        password: widget.password,
        leaveId: request.id,
      );

      if (success) {
        _showSnackBar('تم رفض الإجازة', Colors.orange);
        _refreshData();
      } else {
        _showSnackBar('فشل في رفض الإجازة', Colors.red);
      }
    }
  }

  /// عرض نافذة تأكيد
  Future<bool?> _showConfirmationDialog({
    required String title,
    required String message,
    required String confirmText,
    required Color confirmColor,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: confirmColor,
              foregroundColor: Colors.white,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message), backgroundColor: color));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(AppConfig.lightGrayColor),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        color: Color(AppConfig.primaryColor),
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_allRequests.isEmpty) {
      return _buildEmptyState();
    }

    return _buildMainContent();
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Color(AppConfig.primaryColor)),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل إجازات الموظفين...',
            style: TextStyle(
              color: Color(AppConfig.secondaryTextColor),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Color(AppConfig.errorColor),
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(AppConfig.darkTextColor),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: TextStyle(color: Color(AppConfig.secondaryTextColor)),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _refreshData,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 64,
              color: Color(AppConfig.successColor),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد طلبات معلقة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(AppConfig.darkTextColor),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لا توجد طلبات إجازة من الموظفين تحت إدارتك',
              style: TextStyle(color: Color(AppConfig.secondaryTextColor)),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث باسم الموظف أو نوع الإجازة...',
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 8),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            decoration: BoxDecoration(
              color: Color(AppConfig.primaryColor),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _showFilterDialog,
              icon: const Icon(
                Icons.filter_list,
                color: Colors.white,
                size: 20,
              ),
              tooltip: 'تصفية حسب نوع الإجازة',
              padding: const EdgeInsets.all(8),
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية حسب نوع الإجازة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              initialValue: _selectedLeaveType,
              decoration: const InputDecoration(
                labelText: 'نوع الإجازة',
                border: OutlineInputBorder(),
              ),
              items: _getLeaveTypes().map((type) {
                return DropdownMenuItem(value: type, child: Text(type));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedLeaveType = value ?? 'الكل';
                  _applyFilters();
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedLeaveType = 'الكل';
                _applyFilters();
              });
              Navigator.pop(context);
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  List<String> _getLeaveTypes() {
    Set<String> types = {'الكل'};
    for (var request in _allRequests) {
      types.add(request.leaveTypeName);
    }
    return types.toList();
  }

  Widget _buildFilterAndStatsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Color(AppConfig.whiteColor),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Color(AppConfig.cardShadowColor),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                _isFilterSectionExpanded = !_isFilterSectionExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Color(
                              AppConfig.primaryColor,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.filter_list_rounded,
                            size: 20,
                            color: Color(AppConfig.primaryColor),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'طلبات الإجازة',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Color(AppConfig.darkTextColor),
                                ),
                              ),
                              if (_currentFilter != 'all')
                                Text(
                                  '${_filteredRequests.length} طلب مصفى',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(AppConfig.secondaryTextColor),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      if (_currentFilter != 'all')
                        IconButton(
                          icon: Icon(Icons.clear_rounded, size: 20),
                          onPressed: () => _applyFilter('all'),
                          tooltip: 'عرض الكل',
                          color: Color(AppConfig.errorColor),
                        ),
                      Icon(
                        _isFilterSectionExpanded
                            ? Icons.keyboard_arrow_up_rounded
                            : Icons.keyboard_arrow_down_rounded,
                        color: Color(AppConfig.secondaryTextColor),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (_isFilterSectionExpanded) ...[
            Divider(height: 1, color: Color(AppConfig.dividerColor)),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildQuickStatsMini(),
                  const SizedBox(height: 16),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _buildFilterChip(
                          'الكل',
                          'all',
                          Icons.all_inclusive_rounded,
                        ),
                        const SizedBox(width: 8),
                        _buildFilterChip(
                          'قيد الانتظار',
                          'confirm',
                          Icons.schedule_rounded,
                        ),
                        const SizedBox(width: 8),
                        _buildFilterChip(
                          'موافقة عليه',
                          'validate1',
                          Icons.thumb_up_rounded,
                        ),
                        const SizedBox(width: 8),
                        _buildFilterChip(
                          'معتمدة',
                          'validate',
                          Icons.check_circle_rounded,
                        ),
                        const SizedBox(width: 8),
                        _buildFilterChip(
                          'مرفوضة',
                          'refuse',
                          Icons.cancel_rounded,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Divider(height: 1, color: Color(AppConfig.dividerColor)),
          ],
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        _buildSearchBar(),
        const SizedBox(height: 8),
        _buildFilterAndStatsSection(),
        const SizedBox(height: 16),
        Expanded(child: _buildRequestsList()),
      ],
    );
  }

  Widget _buildQuickStatsMini() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildStatItemMini(
            'الكل',
            _allRequests.length.toString(),
            Colors.grey,
            Icons.all_inclusive_rounded,
          ),
          _buildStatItemMini(
            'قيد الانتظار',
            _pendingCount.toString(),
            const Color(0xFFFF9800),
            Icons.schedule_rounded,
          ),
          _buildStatItemMini(
            'معتمدة',
            _completCount.toString(),
            Colors.green,
            Icons.check_circle_rounded,
          ),
          _buildStatItemMini(
            'موافقة',
            _approvedCount.toString(),
            Colors.blue,
            Icons.thumb_up_rounded,
          ),
          _buildStatItemMini(
            'مرفوضة',
            _rejectedCount.toString(),
            Color(AppConfig.errorColor),
            Icons.cancel_rounded,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItemMini(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Expanded(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, size: 14, color: color),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 9,
              color: Color(AppConfig.secondaryTextColor),
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, IconData icon) {
    final bool isSelected = _currentFilter == value;
    return FilterChip(
      label: Text(label),
      avatar: Icon(icon, size: 16),
      selected: isSelected,
      showCheckmark: false,
      onSelected: (selected) => _applyFilter(selected ? value : 'all'),
      selectedColor: Color(AppConfig.primaryColor),
      checkmarkColor: Colors.white,
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Color(AppConfig.darkTextColor),
      ),
      backgroundColor: Color(AppConfig.lightGrayColor),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      side: BorderSide(
        color: isSelected
            ? Color(AppConfig.primaryColor)
            : Color(AppConfig.dividerColor),
      ),
    );
  }

  Widget _buildRequestsList() {
    return _buildRequestsListView(_filteredRequests);
  }

  Widget _buildRequestsListView(List<PendingLeaveRequest> requests) {
    if (requests.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد طلبات في هذا القسم',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: requests.length,
      itemBuilder: (context, index) {
        final request = requests[index];
        return _buildCompactRequestCard(request, index);
      },
    );
  }

  Widget _buildCompactRequestCard(PendingLeaveRequest request, int index) {
    final isExpanded = _expandedCards.contains(index);
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                if (isExpanded) {
                  _expandedCards.remove(index);
                } else {
                  _expandedCards.add(index);
                }
              });
            },
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Color(request.stateColor).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Color(request.stateColor),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      _getStateIcon(request.state),
                      color: Color(request.stateColor),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          request.employeeName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          request.leaveTypeName,
                          style: TextStyle(
                            color: Color(AppConfig.primaryColor),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          request.dateRange,
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Color(
                            AppConfig.primaryColor,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          request.daysText,
                          style: TextStyle(
                            color: Color(AppConfig.primaryColor),
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Icon(
                        isExpanded ? Icons.expand_less : Icons.expand_more,
                        color: Colors.grey,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          if (isExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (request.description != null &&
                      request.description!.isNotEmpty) ...[
                    const Text(
                      'الوصف:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      request.description!,
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 12),
                  ],
                  _buildActionButtons(request),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء أزرار الإجراء حسب حالة الطلب
  Widget _buildActionButtons(PendingLeaveRequest request) {
    if (request.state == 'confirm') {
      return Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _rejectRequest(request),
              icon: const Icon(Icons.close, size: 18),
              label: const Text('رفض'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(AppConfig.errorColor),
                foregroundColor: Color(AppConfig.whiteColor),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _approveRequest(request),
              icon: const Icon(Icons.check, size: 18),
              label: const Text('موافق'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(AppConfig.successColor),
                foregroundColor: Color(AppConfig.whiteColor),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      );
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Color(request.stateColor).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Color(request.stateColor), width: 1),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getStateIcon(request.state),
            color: Color(request.stateColor),
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            _getStateMessage(request.state),
            style: TextStyle(
              color: Color(request.stateColor),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الحالة
  IconData _getStateIcon(String state) {
    switch (state) {
      case 'validate1':
        return Icons.thumb_up; // موافقة أولى
      case 'validate':
        return Icons.check_circle; // معتمدة نهائياً
      case 'refuse':
        return Icons.cancel; // مرفوضة
      default:
        return Icons.help_outline;
    }
  }

  String _getStateMessage(String state) {
    switch (state) {
      case 'validate1':
        return 'تم الموافقة - في انتظار الاعتماد النهائي';
      case 'validate':
        return 'تم الاعتماد النهائي للطلب';
      case 'refuse':
        return 'تم رفض الطلب';
      default:
        return 'حالة غير معروفة';
    }
  }
}

import 'package:flutter/material.dart';
import '../config/app_config.dart';
import '../services/odoo_service.dart';
import '../services/storage_service.dart';
import '../generated/l10n/app_localizations.dart';

/// شاشة تغيير كلمة المرور
class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isLoading = false;
  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConfig.lightGrayColorObj,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: AppConfig.primaryColorObj,
      foregroundColor: AppConfig.whiteColorObj,
      title: Text(
        AppLocalizations.of(context).changeaddress,
        style: TextStyle(
          fontSize: AppConfig.titleFontSize,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      ),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppConfig.spacing),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسالة تعليمية
            _buildInfoCard(),

            SizedBox(height: AppConfig.largeSpacing),

            // نموذج تغيير كلمة المرور
            _buildPasswordForm(),

            SizedBox(height: AppConfig.largeSpacing),

            // زر التحديث
            _buildUpdateButton(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة المعلومات
  Widget _buildInfoCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(AppConfig.largeSpacing),
      decoration: BoxDecoration(
        color: AppConfig.primaryColorObj.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        border: Border.all(
          color: AppConfig.primaryColorObj.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: AppConfig.primaryColorObj, size: 24),
          SizedBox(width: AppConfig.spacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context).importantInstructions,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppConfig.primaryColorObj,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  AppLocalizations.of(context).passwordRequirements,
                  style: TextStyle(
                    color: AppConfig.darkTextColorObj,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء نموذج كلمة المرور
  Widget _buildPasswordForm() {
    return Container(
      padding: EdgeInsets.all(AppConfig.largeSpacing),
      decoration: BoxDecoration(
        color: AppConfig.whiteColorObj,
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: AppConfig.cardShadowColorObj,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).updatePassword,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppConfig.darkTextColorObj,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppConfig.largeSpacing),

          // كلمة المرور الحالية
          _buildPasswordField(
            controller: _currentPasswordController,
            label: AppLocalizations.of(context).currentPassword,
            hint: AppLocalizations.of(context).enterCurrentPassword,
            obscureText: _obscureCurrentPassword,
            onToggleVisibility: () => setState(
              () => _obscureCurrentPassword = !_obscureCurrentPassword,
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppLocalizations.of(context).enterCurrentPassword;
              }
              return null;
            },
          ),

          SizedBox(height: AppConfig.spacing),

          // كلمة المرور الجديدة
          _buildPasswordField(
            controller: _newPasswordController,
            label: AppLocalizations.of(context).newPassword,
            hint: AppLocalizations.of(context).enterNewPassword,
            obscureText: _obscureNewPassword,
            onToggleVisibility: () =>
                setState(() => _obscureNewPassword = !_obscureNewPassword),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppLocalizations.of(context).enterNewPassword;
              }
              if (value.length < 8) {
                return AppLocalizations.of(context).validationPasswordLength;
              }
              return null;
            },
          ),

          SizedBox(height: AppConfig.spacing),

          // تأكيد كلمة المرور
          _buildPasswordField(
            controller: _confirmPasswordController,
            label: AppLocalizations.of(context).confirmNewPassword,
            hint: AppLocalizations.of(context).confirmPassword,
            obscureText: _obscureConfirmPassword,
            onToggleVisibility: () => setState(
              () => _obscureConfirmPassword = !_obscureConfirmPassword,
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppLocalizations.of(context).confirmPassword2;
              }
              if (value != _newPasswordController.text) {
                return AppLocalizations.of(context).validationPasswordMatch;
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// بناء حقل كلمة المرور
  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required bool obscureText,
    required VoidCallback onToggleVisibility,
    required String? Function(String?) validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: AppConfig.darkTextColorObj,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8),
        TextFormField(
          controller: controller,
          obscureText: obscureText,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: AppConfig.secondaryTextColorObj),
            prefixIcon: Icon(
              Icons.lock_outline,
              color: AppConfig.primaryColorObj,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                obscureText ? Icons.visibility_off : Icons.visibility,
                color: AppConfig.secondaryTextColorObj,
              ),
              onPressed: onToggleVisibility,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              borderSide: BorderSide(color: AppConfig.dividerColorObj),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              borderSide: BorderSide(
                color: AppConfig.primaryColorObj,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              borderSide: BorderSide(color: AppConfig.errorColorObj),
            ),
            filled: true,
            fillColor: AppConfig.whiteColorObj,
          ),
        ),
      ],
    );
  }

  /// بناء زر التحديث
  Widget _buildUpdateButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _updatePassword,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConfig.primaryColorObj,
          foregroundColor: AppConfig.whiteColorObj,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
          elevation: 2,
        ),
        child: _isLoading
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppConfig.whiteColorObj,
                  ),
                ),
              )
            : Text(
                AppLocalizations.of(context).updatePassword,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  /// تحديث كلمة المرور
  Future<void> _updatePassword() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // الحصول على بيانات تسجيل الدخول المحفوظة
      final credentials = await StorageService.getLoginCredentials();

      if (credentials['email'] == null || credentials['password'] == null) {
        if (mounted) {
          _showErrorDialog('لم يتم العثور على بيانات تسجيل الدخول المحفوظة');
        }
        return;
      }

      // إنشاء خدمة Odoo
      final odooService = OdooService(
        baseUrl: AppConfig.defaultServerUrl,
        database: AppConfig.defaultDatabase,
      );

      // المصادقة بكلمة المرور الحالية التي أدخلها المستخدم
      final authResult = await odooService.authenticate(
        credentials['email']!,
        _currentPasswordController.text.trim(),
      );

      if (!authResult.isSuccess) {
        if (mounted) {
          // رسالة خطأ واضحة للمستخدم
          String errorMessage = 'كلمة المرور الحالية غير صحيحة';

          // التحقق من نوع الخطأ لإعطاء رسالة أكثر دقة
          if (authResult.error != null) {
            if (authResult.error!.errorCode == 'INVALID_CREDENTIALS') {
              errorMessage =
                  'كلمة المرور الحالية غير صحيحة. يرجى التأكد من كتابتها بشكل صحيح.';
            } else if (authResult.error!.errorCode == 'CONNECTION_ERROR' ||
                authResult.error!.errorCode == 'NETWORK_ERROR') {
              errorMessage =
                  'لا يمكن الاتصال بالخادم. تحقق من اتصال الإنترنت وحاول مرة أخرى.';
            } else if (authResult.error!.errorCode == 'TIMEOUT_ERROR') {
              errorMessage = 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
            } else {
              errorMessage = authResult.error!.userMessage;
            }
          }

          _showErrorDialog(errorMessage);
        }
        return;
      }

      // تغيير كلمة المرور
      final changeResult = await odooService.executeKw(
        uid: authResult.userId!,
        password: _currentPasswordController.text.trim(),
        model: 'res.users',
        method: 'change_password',
        args: [_currentPasswordController.text, _newPasswordController.text],
      );

      // التحقق من نجاح العملية
      if (changeResult != null) {
        if (mounted) {
          _showSuccessDialog();
        }
      } else {
        if (mounted) {
          _showErrorDialog('فشل في تحديث كلمة المرور. يرجى المحاولة مرة أخرى.');
        }
      }
    } catch (e) {
      if (mounted) {
        // معالجة الأخطاء غير المتوقعة
        String errorMessage =
            'حدث خطأ أثناء تحديث كلمة المرور. يرجى المحاولة مرة أخرى.';

        // التحقق من أنواع الأخطاء الشائعة
        final errorString = e.toString().toLowerCase();
        if (errorString.contains('connection') ||
            errorString.contains('network')) {
          errorMessage =
              'مشكلة في الاتصال بالخادم. تحقق من الإنترنت وحاول مرة أخرى.';
        } else if (errorString.contains('timeout')) {
          errorMessage = 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
        } else if (errorString.contains('authentication') ||
            errorString.contains('login')) {
          errorMessage =
              'كلمة المرور الحالية غير صحيحة. يرجى التأكد من كتابتها بشكل صحيح.';
        }

        _showErrorDialog(errorMessage);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// عرض رسالة النجاح
  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text(AppLocalizations.of(context).success),
          ],
        ),
        content: Text(AppLocalizations.of(context).passwordUpdated),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق الحوار
              Navigator.of(context).pop(); // العودة للإعدادات
            },
            child: Text(AppLocalizations.of(context).ok),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة الخطأ
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text(AppLocalizations.of(context).error),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context).ok),
          ),
        ],
      ),
    );
  }
}

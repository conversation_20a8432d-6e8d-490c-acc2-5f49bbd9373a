/// نموذج طلب الإجازة المعلق للموافقة
class PendingLeaveRequest {
  final int id;
  final String employeeName;
  final int employeeId;
  final String leaveTypeName;
  final int leaveTypeId;
  final DateTime requestDateFrom;
  final DateTime requestDateTo;
  final double numberOfDays;
  final String? description;
  final String state;
  final DateTime? dateFrom;
  final DateTime? dateTo;

  PendingLeaveRequest({
    required this.id,
    required this.employeeName,
    required this.employeeId,
    required this.leaveTypeName,
    required this.leaveTypeId,
    required this.requestDateFrom,
    required this.requestDateTo,
    required this.numberOfDays,
    this.description,
    required this.state,
    this.dateFrom,
    this.dateTo,
  });

  /// إنشاء نموذج من بيانات Odoo
  factory PendingLeaveRequest.fromOdooData(Map<String, dynamic> data) {
    // استخراج اسم الموظف
    final employeeData = data['employee_id'];
    final employeeName = employeeData is List && employeeData.length > 1
        ? employeeData[1].toString()
        : 'غير محدد';
    final employeeId = employeeData is List && employeeData.isNotEmpty
        ? employeeData[0] as int
        : 0;

    // استخراج نوع الإجازة
    final leaveTypeData = data['holiday_status_id'];
    final leaveTypeName = leaveTypeData is List && leaveTypeData.length > 1
        ? leaveTypeData[1].toString()
        : 'غير محدد';
    final leaveTypeId = leaveTypeData is List && leaveTypeData.isNotEmpty
        ? leaveTypeData[0] as int
        : 0;

    // تحويل التواريخ
    DateTime parseDate(dynamic dateStr) {
      if (dateStr == null || dateStr == false) {
        return DateTime.now();
      }
      try {
        return DateTime.parse(dateStr.toString());
      } catch (e) {
        return DateTime.now();
      }
    }

    return PendingLeaveRequest(
      id: data['id'] as int,
      employeeName: employeeName,
      employeeId: employeeId,
      leaveTypeName: leaveTypeName,
      leaveTypeId: leaveTypeId,
      requestDateFrom: parseDate(data['request_date_from']),
      requestDateTo: parseDate(data['request_date_to']),
      numberOfDays: (data['number_of_days'] as num?)?.toDouble() ?? 0.0,
      description: data['name']?.toString(),
      state: data['state']?.toString() ?? 'confirm',
      dateFrom: data['date_from'] != null && data['date_from'] != false
          ? parseDate(data['date_from'])
          : null,
      dateTo: data['date_to'] != null && data['date_to'] != false
          ? parseDate(data['date_to'])
          : null,
    );
  }

  /// تحويل الحالة إلى نص عربي
  String get stateText {
    switch (state) {
      case 'confirm':
        return 'قيد الانتضار';
      case 'validate1':
        return 'تم الموافقة';
      case 'validate':
        return 'تم الاعتماد';
      case 'refuse':
        return 'مرفوضة';
      case 'draft':
        return 'مسودة';
      default:
        return state;
    }
  }

  /// تحويل الحالة إلى لون
  int get stateColor {
    switch (state) {
      case 'confirm':
        return 0xFFFF9800; // برتقالي
      case 'validate1':
        return 0xFF2196F3; // أزرق (موافقة أولى)
      case 'validate':
        return 0xFF4CAF50; // أخضر (معتمدة نهائياً)
      case 'refuse':
        return 0xFFF44336; // أحمر
      case 'draft':
        return 0xFF9E9E9E; // رمادي
      default:
        return 0xFF9E9E9E;
    }
  }

  /// تنسيق فترة الإجازة
  String get dateRange {
    final fromStr =
        '${requestDateFrom.day}/${requestDateFrom.month}/${requestDateFrom.year}';
    final toStr =
        '${requestDateTo.day}/${requestDateTo.month}/${requestDateTo.year}';

    if (requestDateFrom.isAtSameMomentAs(requestDateTo)) {
      return fromStr;
    }

    return '$fromStr - $toStr';
  }

  /// تنسيق عدد الأيام
  String get daysText {
    if (numberOfDays == 1) {
      return 'يوم واحد';
    } else if (numberOfDays == 2) {
      return 'يومان';
    } else if (numberOfDays < 11) {
      return '${numberOfDays.toInt()} أيام';
    } else {
      return '${numberOfDays.toInt()} يوماً';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PendingLeaveRequest &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PendingLeaveRequest{id: $id, employeeName: $employeeName, leaveTypeName: $leaveTypeName, dateRange: $dateRange, numberOfDays: $numberOfDays}';
  }
}
